# -*- coding: utf-8 -*-

'''
先运行基金周报下的月度持仓文件

'''

import pandas as pd
import pymysql
import sqlalchemy as sa


host = "*************"
port = 3306
user = "inforesdep01"
passwd = "tfyfInfo@1602"
db = "wind"
engine = pymysql.connect(user=user, host=host, port=port, passwd=passwd, db=db)
# path_wind = "mysql+pymysql://inforesdep01:tfyfInfo@1602@*************:3306/wind"
# engine = sa.create_engine(path_wind)

dir_name = "E:\\天风金工\\基金仓位周报\\基金风格\\"
# dir_name = 'C:\\Users\\<USER>\\Desktop\\周报\\基金风格\\'


cmd = "select cast(trade_days as signed) Date,S_INFO_EXCHMARKET exchMarket from \
    AShareCalendar where trade_days>20050101"
monthly = pd.read_sql(cmd, engine)
monthly = monthly[monthly['exchMarket']=='SSE']
monthly = monthly.sort_values("Date")
monthly['yymm'] = monthly['Date']//100

trading_monthly1 = monthly.drop_duplicates(['yymm'], keep='last')

dateList = trading_monthly1['Date'].astype(str)

def IdustryCitics2(dateList=None):
    '''
    '''
    cmd="SELECT S_CON_WINDCODE Code, SUBSTR(S_INFO_WINDCODE FROM 5 FOR 4) Industry,\
                S_CON_INDATE InDate,S_CON_OUTDATE OutDate FROM aindexmemberscitics2"
    Industry = pd.read_sql_query(cmd,con=engine)
    Industry = Industry.sort_values('Code')
    Industry['OutDate'] = Industry['OutDate'].fillna('30000000')
    if dateList is None:
        return Industry
    else:
        code_col=[]
        date_col=[]
        ind_col=[]
        for date in dateList:
            temp=Industry[(Industry.InDate <= date)]
            temp=temp[(temp['OutDate'] == "")|(~(temp['OutDate']< date))]
            code_col=code_col+temp.Code.tolist()
            ind_col=ind_col+temp['Industry'].tolist()
            date_col=date_col+[date]*len(temp)
        res=pd.DataFrame({'Code':code_col,'Date':date_col,'Industry':ind_col})
        return res

ind_table = IdustryCitics2(dateList)
ind_table['Date'] = ind_table['Date'].astype(int)
ind_table = ind_table[ [x[0]!='T' for x in ind_table['Code'] ]]
ind_table['Code'] = [ int(x[:6]) for x in ind_table['Code'] ]



ind_table1 = ind_table.drop_duplicates("Code", keep='last')
v = ind_table[ind_table['Date']==20210730]['Industry'].values
ind_table1 = ind_table1[ind_table1['Industry'].isin(v)]


#
cmd = "select * from IndexContrastSector"
ind_name = pd.read_sql(cmd, engine)
ind_fund_map = {x:y for x,y in ind_name[['S_INFO_INDEXCODE','S_INFO_INDUSTRYNAME']].itertuples(index=False) }



#%% 

table_ori = pd.read_csv( dir_name + "fund_benchmark_original210731.csv")
table_par = pd.read_csv( dir_name + "fund_benchmark_partial210731.csv")
table = pd.concat([table_ori,table_par], ignore_index=True)
table = table.groupby(['Code','Date'])['Weight'].sum().reset_index()

def f(temp):
    temp['Weight'] = temp['Weight']/2
    return temp

table = table.groupby('Date').apply(f)
table.index = range(len(table))



# table1 = table.merge(ind_table, on=['Code','Date'])

table1 = table.merge(ind_table1[['Code','Industry']], on=['Code'])


def f(temp):
    temp['Weight'] = temp['Weight']/temp['Weight'].sum()
    return temp

table1 = table1.groupby('Date').apply(f)
table1.index = range(len(table1))


ind_fund = table1.groupby(['Date','Industry'])['Weight'].sum().reset_index()
ind_fund = ind_fund.query('Date>20160101').pivot(index='Industry',columns='Date',values='Weight')
ind_fund = ind_fund.fillna(0)


ind_fund.index = [ 'CI00%s.WI' % str(x) for x in ind_fund.index ]
ind_fund.index = [ ind_fund_map[x] for x in ind_fund.index ]

cols = [ x for x in ind_fund.columns if x>20160101]
cols = cols[::-1]
ind_fund = ind_fund[cols]


# 
date_natural = pd.date_range(start='20160101',end='20260101')
date_natural = pd.DataFrame({"Date":[int(x.strftime("%Y%m%d")) for x in date_natural]})

date_natural['yymm'] = date_natural['Date']//100
date_natural = date_natural.drop_duplicates('yymm',keep='last')

date_natural_map = {x:y for x,y in date_natural[['yymm','Date']].itertuples(index=False) }
ind_fund.columns = [ date_natural_map[x//100] for x in ind_fund.columns ]


#%% 更新


ind_last = pd.read_excel(dir_name+"中信二级行业配置.xlsx").set_index("Unnamed: 0")

cols = [ x for x in ind_fund.columns if x not in ind_last.columns]

res = ind_fund[cols].merge(ind_last,left_index=True,right_index=True,how='outer')

res.to_excel(dir_name+"中信二级行业配置.xlsx")













    