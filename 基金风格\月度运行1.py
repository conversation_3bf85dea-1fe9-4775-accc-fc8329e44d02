# -*- coding: utf-8 -*-


#参数 
report_now = 20250331 #季度

#月度仓位


import mosek
import pandas as pd
import numpy as np
import sqlalchemy as sa
import datetime
import pymysql


# path_wind = "mysql+pymysql://inforesdep01:tfyfInfo@1602@*************:3306/wind"
# engine = sa.create_engine(path_wind)

host = "localhost"
port = 3306
user = "root"
passwd = "root"
db = "wind"
engine = pymysql.connect(user=user, host=host, port=port, passwd=passwd, db=db)



def update_file(ft,file,key):
    f = pd.read_csv( file )
    max_dt = f[key].max()
    ft = ft[ ft[key]>max_dt ]
    if len(ft)>0:
        f = pd.concat( [f,ft],ignore_index=True )
        f.to_csv(file,index=False)
        max_dt = f[key].max()
        print( "update sucessfully! newest is "+str(max_dt) )
    else:
        print( "already newest " + str(max_dt) )


    # 日期

cmd = "select cast(trade_days as signed) Date,S_INFO_EXCHMARKET exchMarket from \
    AShareCalendar where trade_days>20050101"
monthly = pd.read_sql(cmd, engine)
monthly = monthly[monthly['exchMarket']=='SSE']
monthly = monthly.sort_values("Date")
monthly['yymm'] = monthly['Date']//100

trading_monthly1 = monthly.drop_duplicates(['yymm'], keep='last')

yymm_dic = { x//100:x for x in trading_monthly1['Date'] }


start_dt = report_now
year = datetime.date.today().year
report_list = [ x + y*10000 for x in [331,630,930,1231] for y in \
               range(start_dt//10000,year+1) ]
report_list.sort()

def cal_dt_2(x,dt_list):
    if x%10000 == 331:
        start = x//10000*10000 + 400
        end = x//10000*10000 + 900
        return dt_list[(dt_list>start)&(dt_list<end)]
    elif x%10000 == 630:
        start = x//10000*10000 + 700
        end = x//10000*10000 + 1100
        return dt_list[(dt_list>start)&(dt_list<end)]
    elif x%10000 == 930:
        start = x//10000*10000 + 1000
        end = (x//10000+1)*10000 + 200
        return dt_list[(dt_list>start)&(dt_list<end)]
    elif x%10000 == 1231:
        start = (x//10000+1)*10000 + 100
        end = (x//10000+1)*10000 + 500
        return dt_list[(dt_list>start)&(dt_list<end)]
    

report_tradingDt_dic = { x:cal_dt_2(x,monthly['Date']) for x in report_list }


report_list = [ x + y*10000 for x in [331,630,930,1231] for y in \
               range(2005,2030) ]
report_list.sort()

dt_daily = monthly['Date']
ann_list = []
for rep in report_list:
    dt = dt_daily[dt_daily>rep]
    if len(dt)<15:
        break
    dt = dt.iloc[14]
    ann_list.append( dt )

tradingDate_report_ann_dic = { report_list[i]:[ ann_list[i],ann_list[i+1] ] \
                                  for i in range( len(ann_list)-1 ) }

rep_dic = {report_now: report_tradingDt_dic[report_now]}


cmd = "select cast(substr(S_INFO_WINDCODE,1,6) as signed) stock_code, \
    cast(TRADE_DT as signed) Date, S_DQ_ADJCLOSE/S_DQ_ADJPRECLOSE - 1 ret \
  from AShareEODPrices where TRADE_DT>= '20150101'"
price_table = pd.read_sql(cmd,engine)
price_table.columns = ['stock_code','Date','Return']
price_table = price_table.sort_values(['stock_code','Date'])

def cal_temp(ft):
    ft1=ft.sort_values('Date')
    ft1['NAV'] = (ft1['Return']+1).cumprod()
    return ft1

stock_nav_table_daily_dic = {}
for key in rep_dic.keys():
    rep_dateList = rep_dic[key]
    if len(rep_dateList) == 0:
        continue
    else:
        rep_dateList = rep_dateList.to_frame()
    nav_table_daily_temp = price_table.merge(rep_dateList,on=['Date'])
    nav_table_daily_temp = nav_table_daily_temp.groupby('stock_code').apply(\
                                            lambda x:cal_temp(x) )
    nav_table_daily_temp.index = range(len(nav_table_daily_temp))
    stock_nav_table_daily_dic[key] = nav_table_daily_temp
            
            


dt_array = monthly['Date'].values
num = 20
dt_col = []
w_col = []
roll_dt_col = []
for i in range(len(dt_array)):
    if i< num :
        continue
    dt = dt_array[i]
    sub_dt = dt_array[(i-num+1):(i+1)]
    weight = np.arange(1/num,1+1/num,1/num)
    dt_col.extend([dt]*num)
    w_col.extend(weight)
    roll_dt_col.extend(sub_dt)
tw = pd.DataFrame({'Trade_dt':dt_col,'Date':roll_dt_col,'weight':w_col})


tw = tw[ tw['Date']!=20190722 ]
tw = tw[ tw['Date']!=20190808 ]


path1 = "fund_port_ori.csv"
stock_table_ori = pd.read_csv(path1)

path2 = "fund_port_par.csv"
stock_table_par = pd.read_csv(path2)

stock_table_all = pd.concat([ stock_table_ori[['fund_code','stock_code','report_period','weight']],\
                              stock_table_par[['fund_code','stock_code','report_period','weight']] ],\
                                ignore_index=True )

stock_table_all = stock_table_all[stock_table_all['report_period']==report_now]


def getPortfiloRet(fund_portfolio,stock_nav_table_dic,trading_monthly1,split_type='MV',NUM=300):
    def cal_fund_ret_sab(ft):    

        def f_1(ft,price_table_return):
            
            fft = ft.merge( price_table_return,on=['stock_code'] )
            if len(fft) == 0:
                return
            fft['weight_new'] = fft['weight']/np.sum(ft['weight'])
            res = fft.groupby('Date').apply(lambda x: np.sum( x['NAV']*\
                                                     x['weight_new'] ) )
            res = res.reset_index()
            res.columns = ['Date','NAV']
            res['Return'] = [ res['NAV'].iloc[0] -1 ] + res['NAV'].pct_change().tolist()[1:]
            return res[['Date','Return']]
        rep = int( ft['report_period'].values[0] )
        price_table_return = stock_nav_table_dic[ rep ]
        fund_ret_sab = ft.groupby('sign').apply(f_1,price_table_return)
        code = ft['fund_code'].values[0]
        # print( code,rep )
        return fund_ret_sab
    
    if split_type == 'MV':
        # mv_reader = FactorLib.MarketValue.MarketValue()
        s_item = 'MV'
        NUM = NUM
        # f_split = mv_reader.getFactorValue(freq='M').copy()
    elif split_type == 'BP':
        # bp_reader = FactorLib.BP.BP()
        s_item = 'BP'
        NUM = NUM
        # f_split = bp_reader.getFactorValue(freq='M').copy()
    elif split_type == 'MV_BP':
        # calendar = Calendar()
        dt_list = trading_monthly1['Date'].values
        cmd = "select cast(substr(S_INFO_WINDCODE,1,6) as signed) Code,cast(TRADE_DT as \
            signed) Date,S_VAL_MV MV,1/S_VAL_PB_NEW BP from AShareEODDerivativeIndicator \
            where TRADE_DT in("
        for i in dt_list:
            cmd = cmd +"'%i'," % i
        cmd = cmd[:-1]+")"
        f_split = pd.read_sql(cmd, engine)
        
        
    def cal_sign(ft,item,NUM):
        ft1 = ft.sort_values(item)
        ft1['sign'] = [0]*(len(ft)-NUM) + [1]*NUM
        return ft1
    
    def cal_sign2(ft):
        ft1 = ft.sort_values('MV')
        ft1['mv_sign'] = [0]*(len(ft)-300) + [1]*300
        ft1 = ft1.sort_values('BP')
        N = len(ft)//2
        ft1['bp_sign'] = [0]*(len(ft)-N) + [1]*N
        ft1['sign'] = ft1['mv_sign']*10 + ft1['bp_sign']
        return ft1
    
    f_split['yymm'] = [ x//100 for x in f_split['Date'] ]
    f_split = f_split[ [ (x%100)%3 == 0 for x in f_split['yymm'] ] ]
    
    if split_type == 'MV_BP':
        f_split = f_split.groupby('yymm').apply(cal_sign2)
        f_split.index = range(len(f_split))
    elif split_type == 'Industry':
        pass
    else:
        f_split = f_split.groupby('yymm').apply(cal_sign,s_item,NUM)
        f_split.index = range(len(f_split))
    
    f_split = f_split[['Code','yymm','sign']]
    f_split.columns = ['stock_code','yymm','sign']
    
    fund_portfolio['yymm']=[x//100 for x in fund_portfolio['report_period'] ]
    fund_portfolio=fund_portfolio.merge(f_split,on=['stock_code','yymm'])
    
    fund_portfolio_ret=fund_portfolio.groupby(['fund_code','report_period']).\
                                        apply( cal_fund_ret_sab )
    fund_portfolio_ret=fund_portfolio_ret.reset_index()
    
    def f_pivot(ft):
        ft1 = ft[['Date','sign','Return']]
        ft1 = ft1.pivot(index='Date',columns='sign',values='Return')
        return ft1
    
    # 数据转换
    fund_portfolio_ret = fund_portfolio_ret.groupby( ['fund_code','report_period']).\
                                            apply( f_pivot )
    if split_type == 'MV_BP':
        fund_portfolio_ret.rename( columns={0:'r_ss',1:'r_sb',\
                                   10:'r_bs',11:'r_bb'},inplace=True )
    elif split_type == 'Industry':
        fund_portfolio_ret.rename( columns={1:'cycle',2:'consume',\
                                   3:'finance',4:'tmt'},inplace=True )
    else:
        fund_portfolio_ret.rename( columns={0:'r_s',1:'r_b'},inplace=True)
        
    fund_portfolio_ret = fund_portfolio_ret.reset_index()
    fund_portfolio_ret = fund_portfolio_ret.fillna(0)
    
    min_dt = fund_portfolio_ret['Date'].min()-10000
    #合并基金收益率
    cmd = "select F_INFO_WINDCODE fund_code,cast(PRICE_DATE as signed) Date, \
            F_NAV_ADJUSTED nav from ChinaMutualFundNAV where PRICE_DATE>%i and \
            F_INFO_WINDCODE in(" % min_dt
    for i in pd.unique(fund_portfolio_ret['fund_code']):
        cmd = cmd + "'" + i + "',"
    cmd = cmd[:-1] + ")"
    fund_ret = pd.read_sql(cmd, engine)
    
    def cal_r(ft):
        ft1 = ft.sort_values("Date")
        ft1['Return'] = ft1['nav'].pct_change()
        return ft1[['fund_code','Date','Return']]
        
    fund_ret = fund_ret.groupby('fund_code').apply(cal_r)
    fund_ret.index = range(len(fund_ret))
    fund_portfolio_ret = fund_portfolio_ret.merge(fund_ret,on=['fund_code','Date'])
    return fund_portfolio_ret

    
path1 = "fund_port_ori.csv"
stock_table_ori = pd.read_csv(path1)

path2 = "fund_port_par.csv"
stock_table_par = pd.read_csv(path2)

stock_table_all = pd.concat([ stock_table_ori[['fund_code','stock_code','report_period','weight']],\
                              stock_table_par[['fund_code','stock_code','report_period','weight']] ],\
                                ignore_index=True )

stock_table_all = stock_table_all[stock_table_all['report_period']==report_now]

fund_size_bp_ret = getPortfiloRet(stock_table_all, stock_nav_table_daily_dic,
                              trading_monthly1, 'MV_BP')


import datetime

now_dt = int( datetime.datetime.now().strftime("%Y%m%d") )
dt = trading_monthly1['Date'].values
dt = dt[dt<=now_dt]



import mosek
import scipy as sc
import scipy.sparse
import numpy as np

inf = 0.0

class lst:
    
    def __init__(self):
        self.env = mosek.Env()
        
    def solve(self,y,x,weight=None,min_pos=0.6,max_pos=0.8,fix_pos=None):
        
        with self.env.Task(0, 0) as task:

            numvar = x.shape[1]
            task.appendvars( numvar )
            
            if weight is None:
                weight = np.eye( x.shape[0] )
                
            if x.shape[0]!=len(weight):
                raise Exception("wrong length of weight")
            
            weight = np.diag(np.sqrt( weight ) )
            
            y = weight.dot(y)
            x = weight.dot(x)
            Q_object = (x.T.dot(x) * 2)
            Q_object = np.tril(Q_object)

            Q_object =  scipy.sparse.coo_matrix(Q_object)
            i_obj = Q_object.row.tolist()
            j_obj = Q_object.col.tolist()
            v_obj = Q_object.data.tolist()
            task.putqobj(i_obj, j_obj, v_obj)
            c_obj = -2 * y.dot(x)

            low_bon = [0]*numvar
            up_bon = [1]*numvar
            type_bon = [mosek.boundkey.ra]*numvar
            
            task.appendcons(1)
            
            for j in range(numvar):
                task.putcj(j, c_obj[j])
                task.putvarbound(j, type_bon[j], low_bon[j], up_bon[j])
                
            task.putarow( 0, range(numvar), [1]*numvar )   
            
            if fix_pos is None:
                task.putconbound(0,mosek.boundkey.ra,min_pos,max_pos)
            else:
                task.putconbound(0,mosek.boundkey.fx,fix_pos,fix_pos)
            
            task.putobjsense(mosek.objsense.minimize)
            task.optimize()
            solsta = task.getsolsta(mosek.soltype.itr)
        
            xx = [0.] * numvar
            task.getxx(mosek.soltype.itr,xx)
        
            if solsta == mosek.solsta.optimal or solsta == mosek.solsta.near_optimal:
                pass
            elif solsta == mosek.solsta.dual_infeas_cer:
                print("Primal or dual infeasibility.\n")
            elif solsta == mosek.solsta.prim_infeas_cer:
                print("Primal or dual infeasibility.\n")
            elif solsta == mosek.solsta.near_dual_infeas_cer:
                print("Primal or dual infeasibility.\n")
            elif solsta == mosek.solsta.near_prim_infeas_cer:
                print("Primal or dual infeasibility.\n")
            elif mosek.solsta.unknown:
                print("Unknown solution status")
            else:
                print("Other solution status")
            return xx


def cal_pos_reg_wind(ft,cols=['r_big','r_small'],fix_pos=None,
                     fund_type='partial', tradingDate_report_ann_dic=None):

    if len(ft) < 10:
        return
    
    rep = int( ft['report_period'].iloc[0] )
    dt = int( ft['Trade_dt'].iloc[0] )
    
    tradingDate_report_ann_dic = tradingDate_report_ann_dic
    ann = tradingDate_report_ann_dic[rep][0]
    next_ann = tradingDate_report_ann_dic[rep][1]
    if dt<=ann or dt>next_ann:
        return

    y = ft['Return']
    x = ft[cols]
    w = ft['weight']
    if fund_type == 'partial':
        min_pos = 0.3
    elif fund_type == 'original':
        if dt < 20150800:
            min_pos = 0.6
        else:
            min_pos = 0.8
    elif fund_type == 'flex':
        min_pos = 0.0
        
    max_pos = 0.95
    
    leastSquare_solver = lst()
    if not fix_pos is None:
        fix_pos = ft['fore_pos'].iloc[0]
        alc = leastSquare_solver.solve(y,x,w,min_pos,max_pos,fix_pos)
    else:
        alc = leastSquare_solver.solve(y,x,w,min_pos,max_pos)

    w = np.diag(np.sqrt(w))
    y = w.dot(y)
    x = w.dot(x)
    y_fit = x.dot(alc)
    msm = np.sum( (y_fit-y.mean())**2 )
    mse = np.sum( (y-y_fit)**2 ) / (len(y)-1)
    f_value = msm/mse
    return pd.Series( alc+[f_value] ,index = cols+['F'])
    

def estimate(smb2_ret_table, regWeight_table, dt_list=None, items=['r_b','r_s'],
             fund_type='partial', tradingDate_report_ann_dic=None):
    '''
    仓位估计
    '''
    if not dt_list is None:
        regWeight_table = regWeight_table[ regWeight_table['Trade_dt'].\
                                            isin(dt_list) ]
    table_for_PosReg = regWeight_table.merge( smb2_ret_table,on=['Date'] )
    pos_table = table_for_PosReg.groupby(['fund_code','Trade_dt','report_period']).apply( \
        lambda x: cal_pos_reg_wind(x,items,None,fund_type,tradingDate_report_ann_dic) )
    pos_table = pos_table.dropna()
    pos_table = pos_table.reset_index()
    pos_table['fore_pos'] = np.sum( pos_table[items],axis=1 ).values
    if len(items)==2:
        pos_table.rename(columns={'r_b':'PosLarge','r_s':'PosSmall'},inplace=True)
    return pos_table



temp = stock_table_ori[['fund_code','report_period']].drop_duplicates()
fund_size_bp_ori = fund_size_bp_ret.merge(temp,on=['fund_code','report_period'])
fund_size_bp_ori = fund_size_bp_ori[fund_size_bp_ori['Date']<=now_dt]
t = fund_size_bp_ori.groupby('fund_code').size()
median_size = t.median()
t = t[t!=median_size].index.values
fund_size_bp_ori = fund_size_bp_ori[~fund_size_bp_ori['fund_code'].isin(t)]

fund_pos_size_bp_ori = estimate(fund_size_bp_ori, tw,\
                        dt, ['r_ss','r_sb','r_bs','r_bb'],
                    'original', tradingDate_report_ann_dic)
    
fund_pos_size_bp_ori['bp_ratio'] = ( fund_pos_size_bp_ori['r_sb'] + \
        fund_pos_size_bp_ori['r_bb'] ) / fund_pos_size_bp_ori['fore_pos']

fund_pos_size_bp_ori['size_ratio'] = ( fund_pos_size_bp_ori['r_bs'] + \
        fund_pos_size_bp_ori['r_bb'] ) / fund_pos_size_bp_ori['fore_pos']


update_file(fund_pos_size_bp_ori, "fund_pos_size_bp_ori2.csv", "Trade_dt" )



temp = stock_table_par[['fund_code','report_period']].drop_duplicates()
fund_size_bp_par = fund_size_bp_ret.merge(temp,on=['fund_code','report_period'])
fund_size_bp_par = fund_size_bp_par[fund_size_bp_par['Date']<=now_dt]
t = fund_size_bp_par.groupby('fund_code').size()
median_size = t.median()
t = t[t!=median_size].index.values
fund_size_bp_par = fund_size_bp_par[~fund_size_bp_par['fund_code'].isin(t)]

fund_pos_size_bp_par = estimate(fund_size_bp_par, tw,\
                            dt, ['r_ss','r_sb','r_bs','r_bb'],
                            'partial', tradingDate_report_ann_dic)
fund_pos_size_bp_par['bp_ratio'] = ( fund_pos_size_bp_par['r_sb'] + \
        fund_pos_size_bp_par['r_bb'] ) / fund_pos_size_bp_par['fore_pos']

fund_pos_size_bp_par['size_ratio'] = ( fund_pos_size_bp_par['r_bs'] + \
        fund_pos_size_bp_par['r_bb'] ) / fund_pos_size_bp_par['fore_pos']


update_file(fund_pos_size_bp_par, "fund_pos_size_bp_par2.csv", "Trade_dt" )


