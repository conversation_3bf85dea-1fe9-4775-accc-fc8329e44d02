# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import sqlalchemy as sa
import re
import pymysql
import datetime
NOW = int( datetime.datetime.now().strftime("%Y%m%d") )

# engine_path = "mysql+pymysql://inforesdep01:tfyfInfo@1602@*************:3306/wind"
# engine = sa.create_engine(engine_path)

host = "localhost"
port = 3306
user = "root"
passwd = "root"
db = "wind"
engine = pymysql.connect(user=user, host=host, port=port, passwd=passwd, db=db)


class Calendar():

    def __init__(self):
        pass
    
    def getTradingDate(self,freq='D', exchMarket = 'SSE',start =20050000,\
                       end = NOW):
        cmd = "select cast(trade_days as signed) Date,S_INFO_EXCHMARKET exchMarket \
            from AShareCalendar"
        res = pd.read_sql(cmd, engine)
        res = res.sort_values(['exchMarket','Date'])          
        res = res[res['exchMarket']==exchMarket]
        res = res.sort_values('Date')
        res = res[res['Date']>=start]
        # res = res[res['Date']<=NOW]        
        if freq=='D':
            return res['Date']
        elif freq=='M':
            res['M'] = [ x//100 for x in res['Date']]
            res=res.drop_duplicates(subset=['M'],keep='last')
            return res['Date']



class fund_Portfolio_Complete:

    def __init__(self):
        # engine_path = "mysql+pymysql://inforesdep01:tfyfInfo@1602@*************:3306/wind"
        # self.engine = sa.create_engine(engine_path)
        host = "localhost"
        port = 3306
        user = "root"
        passwd = "root"
        db = "wind"
        self.engine = pymysql.connect(user=user, host=host, port=port, passwd=passwd, db=db)
        
    def getFundPool(self):    
        def filter_fund_183(fund_type_wind,fund_name_table,grand_fund):

            def filter_fund(x,y):
                if x[-1] in ['A','B','C','O','H','E','R'] and  y[-1] in \
                            ['A','B','C','O','H','E','R']:
                    if x[:-1] == y[:-1]:
                        return 1
                    return 0
                return 0
            
            cmd = "SELECT * from chinamutualfundsector where S_INFO_SECTOR like '"
            cmd = cmd + fund_type_wind +"'"
            fund_table = pd.read_sql(cmd,self.engine)
            fund_table= fund_table[ ['F_INFO_WINDCODE','S_INFO_SECTORENTRYDT','S_INFO_SECTOREXITDT'] ]
            
            fund_table.columns = ['fund_code','start','end']
            fund_table = fund_table.merge(fund_name_table,on=['fund_code'])            
            fund_table = fund_table.sort_values('fund_name')
            
            fund_table['sign'] =[0] + [ filter_fund(x,y) for x,y in zip(fund_table['fund_name'].values[1:],\
                                      fund_table['fund_name'].values[:-1])]            
            fund_table = fund_table[ fund_table['sign']==0]

            patten_re_hgx = re.compile(".*港.*")
            fund_table=fund_table[ [ (patten_re_hgx.match(x) is None) \
                                  for x in fund_table['fund_name'] ] ]
            fund_table = fund_table[~fund_table['fund_code'].isin(\
                                        grand_fund['fund_code'].values)]
            fund_table = fund_table.fillna('30000000')
            fund_table = fund_table.drop_duplicates(['fund_code'],keep='last')
            fund_table_dic = fund_table.set_index('fund_code')[['start','end']].to_dict('index')

            return fund_table,fund_table_dic


        cmd = "select F_INFO_WINDCODE, F_INFO_NAME from ChinaMutualFundDescription"
        fund_name_table = pd.read_sql(cmd,self.engine)
        fund_name_table.columns = ['fund_code','fund_name']
        
        cmd = "select S_INFO_WINDCODE Mother_fund_code,F_INFO_FEEDER_WINDCODE fund_code from ChinaGradingFund"
        grand_fund = pd.read_sql(cmd,self.engine)

        fund_table_ori,fund_table_ori_dic = filter_fund_183(\
                                    '2001010101000000',fund_name_table,grand_fund)

        fund_table_par,fund_table_par_dic = filter_fund_183(\
                                    '2001010201000000',fund_name_table,grand_fund)

        self.fund_table_ori = fund_table_ori
        self.fund_table_par = fund_table_par
        self.fund_table_ori_dic = fund_table_ori_dic
        self.fund_table_par_dic = fund_table_par_dic


    def getfundPos(self,fund_table_dic,exclude_month=6,exclude_sacel=None,r_type=1):

        def change_freq(ft,daily_dt,monthly_dt):       
            ft1=ft.reset_index()
            ft1.columns=['Date','fund_pos']
            res = daily_dt.merge( ft1[['Date','fund_pos']],on=['Date'],how='outer')
            res =res.sort_values('Date')
            res=res.fillna(method='ffill')
            res = daily_dt.merge(res,on=['Date'],how='left')
            res = res.set_index('Date').shift(1).reset_index().dropna()
            res =res[res['Date'].isin(monthly_dt['Date'])]
            return res
        
        def cal_yymm(x):
            return int(x[:4])*12 + int(x[4:6])
        
        cmd = "SELECT a.S_INFO_WINDCODE fund_code,a.F_PRT_ENDDATE report_period,\
                a.F_PRT_STOCKTONAV fund_pos,a.F_PRT_NETASSET fund_scale,a.F_ANN_DATE ann_dt, \
                b.F_INFO_SETUPDATE setup_date FROM chinamutualfundassetportfolio a,\
                ChinaMutualFundDescription b where a.S_INFO_WINDCODE=b.F_INFO_WINDCODE and \
                a.S_INFO_WINDCODE in ("
        for i in list( fund_table_dic.keys() ):
            cmd = cmd + "'"+i+"',"
        cmd = cmd[:-1]+")"

        fund_pos = pd.read_sql( cmd,self.engine )
        fund_pos = fund_pos[ [ x[4:] in ['0331','0630','0930','1231'] for x in \
                                 fund_pos['report_period'] ] ]

        fund_pos = fund_pos[ [ cal_yymm(x)-cal_yymm(y)>=exclude_month for _,x,y in \
                              fund_pos[['report_period','setup_date']].itertuples() ] ]

        if not exclude_sacel is None:
            fund_pos = fund_pos[ fund_pos['fund_scale']>=exclude_sacel ]
        
        fund_pos1 = fund_pos.groupby(['fund_code']).apply(self.filter_fund,fund_table_dic)
        
        if r_type == 2:
            return fund_pos1
        
        calendar = Calendar()
        daily_dt = pd.DataFrame( {'Date':calendar.getTradingDate().values } )
        monthly_dt = pd.DataFrame( {'Date':calendar.getTradingDate(freq='M').values } )

        fund_pos_median = fund_pos1.groupby('report_period')['fund_pos'].median()
        fund_pos_median = change_freq(fund_pos_median,daily_dt,monthly_dt)
        return fund_pos_median


    def filter_fund(self,port_table,fund_table_dic):

        def choose_fund(ft,dic_1):
            f_c = ft['fund_code'].values[0]
            res = dic_1.get(f_c,None)
            if (res is None):
                return None
            ft=ft[ ft['report_period']<=res['end'] ]
            ft=ft[ ft['report_period']>res['start'] ]
            return ft
        port_table = port_table.groupby('fund_code').apply(choose_fund,fund_table_dic)
        port_table.index = range(len(port_table))
        return port_table
         
    
    def getHalfPortfolio(self,fund_table,start=20101231):

        if type(start) == str:
            start = int(start)
        cmd = "SELECT S_INFO_WINDCODE fund_code,F_PRT_ENDDATE Date,\
            if( right(S_INFO_STOCKWINDCODE,2)='HK',999999,cast(substr(\
                S_INFO_STOCKWINDCODE,1,6) as signed) ) stock_code,\
            F_PRT_STKVALUETONAV weight,F_PRT_STKVALUE value,ANN_DATE from ChinaMutualFundStockPortfolio \
            where right(F_PRT_ENDDATE,4) in ('0630','1231') and S_INFO_WINDCODE in("

        for i in pd.unique(fund_table):
            cmd = cmd + "'" + i + "'"+","
        cmd = cmd[:-1]+") and F_PRT_ENDDATE>=%i" % ( start-10000 )     
        fund_portfolio = pd.read_sql(cmd,self.engine)
        fund_portfolio.columns = ['fund_code', 'report_period', 'stock_code', \
                                  'weight', 'value', 'ann_date']
        def f_f1(ft):
            if np.sum( ft['stock_code']==999999 ) >0:
                return None
            return ft
        
        fund_portfolio = fund_portfolio.groupby(['fund_code', 'report_period']).\
                            apply(f_f1).dropna()
        fund_portfolio.index = range(len(fund_portfolio))

        cmd = "SELECT a.S_INFO_WINDCODE fund_code,a.F_PRT_ENDDATE report_period,\
                a.F_PRT_NETASSET fund_scale, b.F_INFO_SETUPDATE setup_date \
                FROM chinamutualfundassetportfolio a,\
                ChinaMutualFundDescription b where a.S_INFO_WINDCODE=b.F_INFO_WINDCODE and \
                a.S_INFO_WINDCODE in ("
        for i in  np.unique(fund_portfolio['fund_code']):
            cmd = cmd + "'"+i+"',"
        cmd = cmd[:-1]+")"

        fund_pos = pd.read_sql( cmd,self.engine )
        fund_pos = fund_pos[ [ x[4:] in ['0630','1231'] for x in \
                                 fund_pos['report_period'] ] ]
        def cal_yymm(x):
            return int(x[:4])*12 + int(x[4:6])
        
        fund_portfolio = fund_portfolio.merge(fund_pos,on=['fund_code','report_period'])

        fund_portfolio = fund_portfolio[ [ cal_yymm(x)-cal_yymm(y)>=6 for _,x,y in \
                    fund_portfolio[['report_period','setup_date']].itertuples() ] ]
        
        fund_portfolio = fund_portfolio[ fund_portfolio['fund_scale']>=5*10**7 ]

        return fund_portfolio
        
        
    def completePortfolio(self,fund_table,start=20101231,end=None):

        if type(start) == str:
            start = int(start)
        if end is None:
            end = 30000000
    
        calendar = Calendar()
        tradingDate_Monthly = calendar.getTradingDate(freq='M',start=start-10000)
        tradingDate_Monthly = tradingDate_Monthly[tradingDate_Monthly<=end]

        cmd = "SELECT S_INFO_WINDCODE fund_code,F_PRT_ENDDATE Date,\
            if( right(S_INFO_STOCKWINDCODE,2)='HK',999999,cast(substr(\
                S_INFO_STOCKWINDCODE,1,6) as signed) ) stock_code,\
            F_PRT_STKVALUETONAV weight,F_PRT_STKVALUE value,ANN_DATE from ChinaMutualFundStockPortfolio \
            where S_INFO_WINDCODE in("

        for i in pd.unique(fund_table):
            cmd = cmd + "'" + i + "'"+","
        cmd = cmd[:-1] + ") and F_PRT_ENDDATE>=%i and F_PRT_ENDDATE<=%i" \
                         % ( start-10000,end )     
        fund_portfolio = pd.read_sql(cmd,self.engine)
        fund_portfolio.columns = ['fund_code', 'report_period', 'stock_code', \
                                  'weight', 'value', 'ann_date']
        
        def f_f1(ft):
            if np.sum( ft['stock_code']==999999 ) >0:
                return None
            return ft
        
        fund_portfolio = fund_portfolio.groupby(['fund_code', 'report_period']).\
                            apply(f_f1).dropna()
        fund_portfolio.index = range(len(fund_portfolio))
        
        def getIndustrySAC(dateList=None):
            cmd1 = "SELECT cast(substr(S_INFO_WINDCODE,1,6) as signed) Code,SEC_IND_CODE,\
                cast(ENTRY_DT as signed) ENTRY_DT ,cast(REMOVE_DT as signed) REMOVE_DT from \
                    asharesecnindustriesclass"
            Industry1=pd.read_sql_query(cmd1,con=engine)
            Industry1=Industry1[~Industry1['ENTRY_DT'].isnull()]
            Industry1.REMOVE_DT.fillna(30000000, inplace=True)
            Industry1['ENTRY_DT']=[ 20121231 if (x<20121231) else x for x in Industry1['ENTRY_DT']]
        
            #旧
            cmd2 = "SELECT cast(substr(S_INFO_WINDCODE,1,6) as signed) Code,SEC_IND_CODE,\
                   cast(ENTRY_DT as signed) ENTRY_DT ,cast(REMOVE_DT as signed) REMOVE_DT \
                    from asharesecindustriesclass"
            Industry2=pd.read_sql_query(cmd2,con=engine)
            Industry2 = Industry2[Industry2['ENTRY_DT']<20121231]
            Industry2.REMOVE_DT.fillna(30000000, inplace=True)
            Industry2['REMOVE_DT']=[20121231 if (x>20121231) else  x for x in Industry2['REMOVE_DT']]

            Industry = pd.concat([Industry1,Industry2],ignore_index=True)
            Industry=Industry.sort_values('Code')

            # dateList = tradingDate_Monthly

            if dateList is None:
                return Industry
            else:
                if type(dateList) == list:
                    dateList = pd.Series(dateList)
                code_col=[]
                date_col=[]
                ind_col=[]
                for a,code,industy,InDate,OutDate in Industry.itertuples():
                    dt = dateList[(dateList>InDate)&(dateList<=OutDate) ]
                    code_col.extend( [code]*len(dt) )
                    date_col.extend( dt.tolist() )
                    ind_col.extend( [industy]*len(dt) )   
                res=pd.DataFrame({'Code':code_col,'Date':date_col,'IndustryCode':ind_col})
                
                res['Industry'] = [ chr(int(x[:4])-336) if int(x[:4])<1200 else chr(int(x[:4])-1136)\
                                   for x in res['IndustryCode']]
                return res
            
        def get_ind_sac(tradingDateListNew,adj=False):
            # print(tradingDateListNew)
            industry_sac = getIndustrySAC(tradingDateListNew)
            # print(industry_sac)
            industry_sac['yymm'] = [ x//100 for x in industry_sac['Date'] ]
            industry_sac_1_1 = industry_sac[['Code','yymm','Industry']]
            industry_sac_1_1.columns = ['stock_code','yymm','Industry']    
            yymm_list = pd.Series([ x//100 for x in tradingDateListNew] )
            # print(industry_sac_1_1)
            def adj_ind(ft):
                ft1=ft.sort_values('yymm')
                code = ft1['stock_code'].values[0]
                now = ft1['yymm'].values[0]
                ind = ft1['Industry'].values[0]
                append_date_list = yymm_list[yymm_list < now ].tail(6).tolist()
                append_table = pd.DataFrame({'stock_code':[code]*len(append_date_list),'yymm':append_date_list,\
                                             'Industry':[ind]*len(append_date_list)})
                ft1=pd.concat([ft1,append_table],ignore_index=True)
                return ft1
            
            if adj == True:
                industry_sac_fill = industry_sac_1_1.groupby('stock_code').apply(adj_ind)
                industry_sac_fill.index=range(len(industry_sac_fill))
            else:
                industry_sac_fill = industry_sac_1_1
            
            # industry 20121231 用2013年行业分类 
            industry_sac_20121231 = industry_sac_fill[industry_sac_fill['yymm'] == 201301 ].copy()
            industry_sac_20121231['yymm'] = 201212
        
            return industry_sac_fill,industry_sac_20121231
        # print("1")
        industry_sac,industry_sac_20121231 = get_ind_sac(tradingDate_Monthly,True)
                
        cmd = "select cast(substr(S_INFO_WINDCODE,1,6) as signed) stock_code,S_INFO_LISTDATE \
            list_dt from AShareDescription where (SUBSTR(S_INFO_WINDCODE,1,1) REGEXP '[^0-9.]')= 0 "
        ashare_list_dt = pd.read_sql(cmd,self.engine).dropna()
        
        fund_NonTop10 = self.getNonTop10(fund_portfolio,ashare_list_dt)
        fund_NonTop10 = fund_NonTop10.merge(industry_sac,on=['stock_code','yymm'])
        
        fund_Top10 = self.getTop10(fund_portfolio)
        fund_Top10 = fund_Top10.merge( industry_sac,on=['stock_code','yymm'] )
        fund_Top10_g = fund_Top10.groupby(['fund_code','report_period','Industry'])
        
        fund_Top10_ind = self.get_Top10_Ind(fund_Top10)

        fund_NonTop10_Ind = self.get_NonTOP10_ind(fund_Top10_ind)
        fund_NonTop10_Ind_g = fund_NonTop10_Ind.groupby(['fund_code','report_period'] )
        NonTop10_20121231_ind= self.get_NonTOP10_ind_20121231(fund_NonTop10,industry_sac_20121231)
        NonTop10_20121231_ind_g = NonTop10_20121231_ind.groupby( 'fund_code' )

        tf_pool_sac_group = self.getStock2fill( industry_sac )
        
        fund_NonTop10_fill = fund_NonTop10.groupby( ['fund_code','report_period'] ).\
               apply( lambda x: self.cal_fill_portfolio(x,\
                                fund_Top10_g,\
                                fund_NonTop10_Ind_g,\
                                NonTop10_20121231_ind_g,\
                                tf_pool_sac_group) )

        fund_NonTop10_fill = fund_NonTop10_fill.reset_index(0)
        fund_NonTop10_fill.index = range(len(fund_NonTop10_fill))
        
        # 全部持仓
        all_portfolio_fill = self.add_all_stock(fund_NonTop10_fill,fund_Top10)
        
        return all_portfolio_fill



    def add_all_stock(self,fund_NonTop10_fill,fund_Top10):
        def f_temp(ft):
            f_c = ft['fund_code'].values[0]
            rep=ft['report_period'].values[0]
            if fund_Top10_g.groups.get( (f_c,rep),None ) is None:
                return
            top10 = fund_Top10_g.get_group( (f_c,rep) )
            temp  = top10[ ['fund_code','stock_code','weight','Industry','report_period']]
            all_1 = pd.concat( [temp,ft],ignore_index=True)
            all_1 = all_1.groupby(['fund_code','stock_code','report_period','Industry'])['weight'].sum()
            all_1 = all_1.reset_index()
            return all_1
        
        fund_Top10_g = fund_Top10.groupby( ['fund_code','report_period'] )
        res = fund_NonTop10_fill.groupby( ['fund_code','report_period'] ).apply(f_temp)
        res.index = range(len(res))
        return res


    def getStock2fill(self,industry_sac):        
        calendar = Calendar()
        tradingDate_Monthly = calendar.getTradingDate(freq='M',start=20050101)
        
        cmd = " select cast(substr(S_INFO_WINDCODE,1,6) as signed) stock_code,\
        cast(TRADE_DT as signed) Date,S_VAL_MV MV from AShareEODDerivativeIndicator \
            where TRADE_DT in( "
        for i in tradingDate_Monthly:
            cmd = cmd + str(i) + ","
        cmd = cmd[:-1] + ")"
        tf_pool_sac = pd.read_sql(cmd, self.engine)
        tf_pool_sac['yymm'] = [ x//100 for x in tf_pool_sac['Date'] ]
        tf_pool_sac = tf_pool_sac.merge(industry_sac[['stock_code','yymm','Industry']],\
                                        on = ['stock_code','yymm'])   

        tf_pool_sac['Date'] =[ x//100*100+30 if (x//100%100) in [6,9] else\
                               x//100*100+31 for x in tf_pool_sac['Date'] ]
        tf_pool_sac['Date'] = tf_pool_sac['Date'].astype( str )
        
        def f_temp(ft):
            ft = ft.sort_values('MV')
            N = len(ft)//2
            ft1 =ft.iloc[N-1:N+2,:].copy()
            return ft1
            
        tf_pool_sac = tf_pool_sac.groupby(['Date','Industry']).apply(f_temp)    
        tf_pool_sac.index=range(len(tf_pool_sac))  
        tf_pool_sac_group = tf_pool_sac.groupby(['Date','Industry'])
        return tf_pool_sac_group


    def get_Top10_Ind(self,fund_portfolio_top10):
        fund_TOP10_ind = fund_portfolio_top10.groupby(['fund_code','report_period',\
                                             'Industry'])['weight'].sum()
        fund_TOP10_ind = fund_TOP10_ind.reset_index()
        
        return fund_TOP10_ind



    def get_NonTOP10_ind(self,fund_Top10_ind):
        fund_table = pd.unique( fund_Top10_ind['fund_code'] )

        cmd = "SELECT S_INFO_WINDCODE fund_code,F_PRT_ENDDATE report_period,\
                S_INFO_CSRCINDUSCODE Industry,F_PRT_INDUSTONAV tot_weight FROM \
                ChinaMutualFundIndPortfolio where S_INFO_WINDCODE in("
        for i in fund_table:
            cmd = cmd + "'" + i + "',"
        cmd = cmd[:-1]+")"
        fund_ind_ratio = pd.read_sql(cmd,self.engine)
        fund_ind_ratio = fund_ind_ratio[fund_ind_ratio['report_period']>'20050000']

        fund_ind_ratio = fund_ind_ratio[ [ len(x)==1 for x in fund_ind_ratio['Industry']]]

        temp = fund_Top10_ind[['fund_code','report_period']] 
        temp = temp.drop_duplicates(['fund_code','report_period'])
        fund_nonTOP_ind = fund_ind_ratio.merge(temp,on=['fund_code','report_period'])

        fund_nonTOP_ind = fund_nonTOP_ind.merge(fund_Top10_ind,on = \
                ['fund_code', 'report_period','Industry'],how='left' )

        fund_nonTOP_ind['weight'] = fund_nonTOP_ind['weight'].fillna(0)    
        fund_nonTOP_ind['non_heavy_weight'] = fund_nonTOP_ind['tot_weight'] - \
                                                fund_nonTOP_ind['weight']

        def filter_f(ft):
            if np.sum(ft['non_heavy_weight'] < -1):
                return
            else:
                return ft
        fund_nonTOP_ind = fund_nonTOP_ind.groupby(['fund_code','report_period']).apply(filter_f)
        fund_nonTOP_ind.index=range(len(fund_nonTOP_ind))

        fund_nonTOP_ind = fund_nonTOP_ind[fund_nonTOP_ind['non_heavy_weight']>0.1]
        
        return fund_nonTOP_ind


    def get_NonTOP10_ind_20121231(self,fund_NonTop10,industry_sac_20121231):
        
        fund_NonTop10 = fund_NonTop10[fund_NonTop10['report_period']=='20121231'].copy()
        fund_NonTop10 = fund_NonTop10[['fund_code','stock_code','report_period','yymm','weight']]
        fund_NonTop10 = fund_NonTop10.merge( industry_sac_20121231,\
                                             on=['stock_code','yymm'] )
        
        NonTop10_20121231_ind = fund_NonTop10.groupby(['fund_code',\
                                    'report_period','Industry'])['weight'].sum()
        
        NonTop10_20121231_ind = NonTop10_20121231_ind.reset_index()
        
        NonTop10_20121231_ind.columns = ['fund_code','report_period','Industry','non_heavy_weight']
        
        return NonTop10_20121231_ind


    def getNonTop10(self,fund_portfolio,ashare_list_dt=None):

        def cal_yymm(x):
            return int(x[:4])*12 + int(x[4:6])
        
        nonTop10 =fund_portfolio[ [ x[4:] in ['0630','1231'] for x in \
                                   fund_portfolio['report_period'] ] ].copy()
        nonTop10 = nonTop10[ [ int(x[4:6]) not in \
                        [7,1] for x in nonTop10['ann_date'] ] ]
    
        nonTop10['yymm'] = [ int(x[:6]) for x in nonTop10['report_period'] ]
                  
        nonTop10 = nonTop10[ nonTop10['weight']>0.01 ]
        
        if ashare_list_dt is None:
            return nonTop10
        
        nonTop10 = nonTop10.merge(ashare_list_dt,on='stock_code')
        nonTop10 = nonTop10[ [ cal_yymm(x)-cal_yymm(y) >=2 for x,y in nonTop10[\
                    ['report_period','list_dt'] ].itertuples(index=False)] ]
        return nonTop10

    
    def getTop10(self,fund_portfolio):

        port = fund_portfolio
        
        sign = np.where((port['report_period'].astype(int)%10000).isin([331,930]),
                        True,False)
        
        sign = np.where( ((port['report_period'].astype(int)%10000)==1231) & 
                         ((port['ann_date'].astype(int)%10000//100)==1),True,sign)
                         
        sign = np.where( ((port['report_period'].astype(int)%10000)==630) & 
                         ((port['ann_date'].astype(int)%10000//100)==7),True,sign)
        
        Top10 = port[ sign ].copy()
        Top10['yymm'] = [ int(x[:6]) for x in Top10['report_period'] ]
        return Top10


    def cal_fill_portfolio(self,ft,fund_portfolio_top10_group,fund_portfolio_non_heavy_indWeight_group,\
                           non_top10_20121231_indWeight_group,tf_pool_sac_group):

        f_c = ft['fund_code'].values[0]
        rep = ft['report_period'].values[0]
    
        if int(rep[4:6])==6:
            rep1 = rep[:4]+'0930'
            rep2 = rep[:4]+'1231'
        else:
            rep1 = str(int(rep[:4])+1)+'0331'
            rep2 = str(int(rep[:4])+1)+'0630'
        
        ft_group = ft[['stock_code','weight','Industry']].groupby('Industry')

        if rep =='20121231':
            if not non_top10_20121231_indWeight_group.groups.get( f_c,None ) is None:
                weight_0 = non_top10_20121231_indWeight_group.get_group(f_c)
                weight_0 = weight_0.set_index('Industry')['non_heavy_weight'].to_dict()
            else:
                return
        else:
            if not fund_portfolio_non_heavy_indWeight_group.groups.get( (f_c,rep),None ) is None:
                weight_0 = fund_portfolio_non_heavy_indWeight_group.get_group( (f_c,rep) )
                weight_0 = weight_0.set_index('Industry')['non_heavy_weight'].to_dict()
            else:
                return 
    
        chunks = []    
        for rep_1 in [rep1,rep2]:
            if not fund_portfolio_non_heavy_indWeight_group.groups.get( (f_c,rep_1),None) is None:
                weight_1 = fund_portfolio_non_heavy_indWeight_group.get_group( (f_c,rep_1) )
                weight_1 = weight_1.set_index('Industry')['non_heavy_weight'].to_dict()
            else:
                continue
            
            for a,b in weight_1.items():
                weight_last = weight_0.get(a,0)
                if weight_last == 0 or ft_group.groups.get(a,None) is None:
                    if not fund_portfolio_top10_group.groups.get((f_c,rep,a),None) is None:
                        temp = fund_portfolio_top10_group.get_group((f_c,rep,a),None)
                        weight_new =temp[['stock_code','weight','Industry']].copy()
                        weight_new['weight'] = weight_new['weight']*b/np.sum(weight_new['weight'])
                        weight_new['report_period'] = rep_1
                        weight_new['weight'] = [10 if x>10 else x for x in weight_new['weight'] ]
                        left_weight = b - np.sum(weight_new['weight'])
                        if left_weight>0.1:
                            temp = tf_pool_sac_group.get_group( (rep_1,a) )
                            weight_new_1 =temp[['stock_code','Industry']].copy()
                            weight_new_1['weight'] = left_weight/len(temp)
                            weight_new_1 = weight_new_1[['stock_code','weight','Industry']]               
                            weight_new_1.columns = ['stock_code','weight','Industry']
                            weight_new = pd.concat([weight_new,weight_new_1],ignore_index=True)
                        chunks.append(weight_new)
                    else:
                        if a =='O':
                            continue
                        temp = tf_pool_sac_group.get_group( ( rep_1,a ) )
                        weight_new =temp[['stock_code','Industry']].copy()
                        weight_new['weight'] = b/len(temp)
                        weight_new = weight_new[['stock_code','weight','Industry']]
                        weight_new.columns = ['stock_code','weight','Industry']
                        weight_new['report_period'] = rep_1
                        chunks.append(weight_new)
                else:
                    weight_new = ft_group.get_group(a).copy()
                    weight_new['weight'] = weight_new['weight']*b/np.sum(weight_new['weight'])
                    weight_new['report_period'] = rep_1
                    weight_new['weight'] = [10 if x>10 else x for x in weight_new['weight'] ]
                    left_weight = b - np.sum(weight_new['weight'])
                    if left_weight>0.1:
                        temp = tf_pool_sac_group.get_group( (rep_1,a) )
                        weight_new_1 =temp[['stock_code','Industry']].copy()
                        weight_new_1['weight'] = left_weight/len(temp)
                        weight_new_1 = weight_new_1[['stock_code','weight','Industry']]               
                        weight_new_1.columns = ['stock_code','weight','Industry']
                        weight_new_1['report_period'] = rep_1
                        weight_new = pd.concat([weight_new,weight_new_1],ignore_index=True)
                    chunks.append(weight_new)
        
        if len(chunks) == 0:
            return
        
        return pd.concat(chunks,ignore_index=True)



if __name__ == "__main__":
    pass







