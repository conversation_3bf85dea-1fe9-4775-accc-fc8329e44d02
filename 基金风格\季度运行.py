# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import datetime
now_dt = int( datetime.datetime.now().strftime("%Y%m%d") )
# import sqlalchemy as sa
import pymysql
# path_wind = "mysql+pymysql://inforesdep01:tfyfInfo@1602@*************:3306/wind"
# engine = sa.create_engine(path_wind)

host = "localhost"
port = 3306
user = "root"
passwd = "root"
db = "wind"
engine = pymysql.connect(user=user, host=host, port=port, passwd=passwd, db=db)

def update_file(ft,file,key):
    f = pd.read_csv( file )
    max_dt = f[key].max()
    ft = ft[ ft[key]>max_dt ]
    if len(ft)>0:
        f = pd.concat( [f,ft],ignore_index=True )
        f.to_csv(file,index=False)
        max_dt = f[key].max()
        print( "update sucessfully! newest is "+str(max_dt) )
    else:
        print( "already newest " )
    
    
def update_file1(ft,file,key):
    f = pd.read_csv( file )
    dt_list = np.unique( f[key] )
    ft = ft[ ~ft[key].isin(dt_list) ]
    if len(ft)>0:
        f = pd.concat( [f,ft],ignore_index=True )
        f.to_csv(file,index=False)
        max_dt = ft[key].max()
        print( "update sucessfully! newest is "+str(max_dt) )
    else:
        print( "already newest " )


def execute():
    #日期    
    now = int( datetime.datetime.now().strftime("%Y%m%d") )
    
    cmd = "select cast(trade_days as signed) Date,S_INFO_EXCHMARKET exchMarket from \
        AShareCalendar where trade_days>20050101"
    monthly = pd.read_sql(cmd, engine)
    monthly = monthly[monthly['exchMarket']=='SSE']
    monthly = monthly.sort_values("Date")
    monthly['yymm'] = monthly['Date']//100
    
    trading_monthly1 = monthly.drop_duplicates(['yymm'], keep='last')
    
    yymm_dic = { x//100:x for x in trading_monthly1['Date'] }
    
    dt_now = monthly[monthly['Date']<now]['Date'].iloc[-1]
    last_month = trading_monthly1[trading_monthly1['Date']<=dt_now]['Date'].iloc[-1]
    
    trading_monthly2 = trading_monthly1.copy()
    
    def getReport(date):
        year = date//10000*10000
        month = date//100%100
        if month <= 3:
            report = year - 10000 + 1231
        elif month <= 6:
            report = year + 331
        elif month <= 9:
            report = year + 630
        elif month <= 12:
            report = year + 930
        return report
    
    trading_monthly2['report'] = [getReport(x) for x in trading_monthly2['Date']]
    monthly_11 = monthly.merge(trading_monthly2[['Date','report']], on=['Date'], how='left')
    monthly_11['report'] = monthly_11['report'].fillna(method='ffill')
    monthly_11 = monthly_11.dropna()
    monthly_11['report'] = monthly_11['report'].astype(int)
    monthly_11 = {x:y for x,y in monthly_11[['Date','report']].itertuples(index=False) }
    report_now = monthly_11[dt_now]

    temp = pd.read_csv("fund_port_ori.csv")
    reports = np.unique(temp['report_period'])
    import fundPortComplete
    fund_complete = fundPortComplete.fund_Portfolio_Complete()
    
    if report_now in reports:
        print("already newest.")
    else:
        fund_complete.getFundPool()        
        start_dt =  20200101
        fundcode_list = np.unique( fund_complete.fund_table_ori['fund_code'].tolist()+\
                            fund_complete.fund_table_par['fund_code'].tolist() )
        
        fund_port_fill = fund_complete.completePortfolio(fundcode_list, start = start_dt )
        
        # 普通
        fund_port_ori = fund_complete.filter_fund(fund_port_fill,fund_complete.fund_table_ori_dic)
        
        fund_port_ori['report_period'] = fund_port_ori['report_period'].astype(int)
        
        update_file(fund_port_ori, "fund_port_ori.csv","report_period" )
        
        update_file1(fund_port_ori[['fund_code', 'stock_code', 'report_period','weight']], 
                     "fund_ori_adj.csv" , "report_period" )
        
        #偏股
        fund_port_par = fund_complete.filter_fund(fund_port_fill,fund_complete.fund_table_par_dic)
        
        fund_port_par['report_period'] = fund_port_par['report_period'].astype(int)
        
        update_file(fund_port_par, "fund_port_par.csv", "report_period" )
        
        update_file1(fund_port_par[['fund_code', 'stock_code','report_period','weight']], 
                     "fund_par_adj.csv" , "report_period" )
       
    # 基金池
    if (now_dt//100%100==[3,8] and now_dt%100<30) or (now_dt//100%100 in [1,2,7]):
        pass
    else:
        fund_complete.getFundPool()
        
        fund_port_half = fund_complete.getHalfPortfolio(pd.unique( \
                            fund_complete.fund_table_ori['fund_code'].tolist()+\
                            fund_complete.fund_table_par['fund_code'].tolist() ),\
                            start = 20200101 )
        fund_ori_half = fund_complete.filter_fund(fund_port_half, fund_complete.fund_table_ori_dic)
        
        fund_ori_half['report_period'] = fund_ori_half['report_period'].astype(int)
        
        fund_ori_half['report_period'] = fund_ori_half['report_period'] + 1000
        
        update_file1(fund_ori_half[['fund_code', 'stock_code', 'report_period','weight']], 
                     "fund_ori_adj.csv" , "report_period" )
    
        fund_par_half = fund_complete.filter_fund(fund_port_half,fund_complete.fund_table_par_dic)
        
        fund_par_half['report_period'] = fund_par_half['report_period'].astype(int)
        
        fund_par_half['report_period'] = fund_par_half['report_period'] + 1000
        
        update_file1(fund_par_half[['fund_code', 'stock_code', 'report_period','weight']], 
                     "fund_par_adj.csv" , "report_period" )
    print("file1.")
    

execute()







