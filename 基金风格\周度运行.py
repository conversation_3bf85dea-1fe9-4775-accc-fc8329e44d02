# -*- coding: utf-8 -*-

#参数

report_now = 20250331 #报告期
last_month = 20250530 #上个月末
dt_now = 20250704 #日期

import pandas as pd
import numpy as np
import pymysql
import sqlalchemy as sa
# import xlwings as xw  # 不再使用xlwings
import sys
import os
import warnings

warnings.filterwarnings('ignore')
# path_wind = "mysql+pymysql://inforesdep01:tfyfInfo@1602@*************:3306/wind"
# engine = sa.create_engine(path_wind)
host = "localhost"
port = 3306
user = "root"
passwd = "root"
db = "wind"
engine = pymysql.connect(user=user, host=host, port=port, passwd=passwd, db=db)


cur_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(cur_dir)


# 日期

cmd = "select cast(trade_days as signed) Date,S_INFO_EXCHMARKET exchMarket from \
    AShareCalendar where trade_days>20050101"
monthly = pd.read_sql(cmd, engine)
monthly = monthly[monthly['exchMarket']=='SSE']
monthly = monthly.sort_values("Date")
monthly['yymm'] = monthly['Date']//100

trading_monthly1 = monthly.drop_duplicates(['yymm'], keep='last')

yymm_dic = { x//100:x for x in trading_monthly1['Date'] }



Industry = pd.read_sql_query("SELECT cast(substr(S_CON_WINDCODE from 1 for 6) \
      as signed) Code, cast(SUBSTR(S_INFO_WINDCODE FROM 5 FOR 4) as signed) \
      Industry, cast(S_CON_INDATE as signed) InDate,cast(S_CON_OUTDATE as signed) \
      OutDate FROM aindexmemberscitics where (SUBSTR(S_CON_WINDCODE,1,1) \
      REGEXP '[^0-9.]')= 0", con=engine)
Industry=Industry.sort_values('Code')
Industry = Industry[ ~Industry['InDate'].isnull() ]
# Industry=Industry.replace( {None:30000000} )
Industry['OutDate']=Industry['OutDate'].fillna(30000000)

code_col=[]
date_col=[]
ind_col=[]
for a,code,industy,InDate,OutDate in Industry.itertuples():
    dateList = monthly['Date'].values
    dt = dateList[(dateList>=InDate)&(dateList<=OutDate) ]
    code_col.extend( [code]*len(dt) )
    date_col.extend( dt.tolist() )
    ind_col.extend( [industy]*len(dt) )
    
industry_citics=pd.DataFrame({'Code':code_col,'Date':date_col,'Industry':ind_col})
        
        
cycle_industry = list(range(5001,5014)) +[5024,5029]
finance_industry = [5021,5022,5023]
tmt_industry = [5025,5026,5027,5028]
consume_industry = [5014,5015,5016,5017,5018,5019,5020 ]

industry_dic = {}
industry_dic.update( {x:1 for x in cycle_industry} )
industry_dic.update( {x:2 for x in consume_industry} )
industry_dic.update( {x:3 for x in finance_industry} )
industry_dic.update( {x:4 for x in tmt_industry} )




cmd = "SELECT S_INFO_WINDCODE IndexCode, cast(TRADE_DT as signed) Date, S_DQ_CLOSE from aindexindustrieseodcitics WHERE SUBSTR(S_INFO_WINDCODE FROM 5 FOR 4) >'5000' \
    AND SUBSTR(S_INFO_WINDCODE FROM 5 FOR 4) <='5030' AND TRADE_DT>'%i' \
                  ORDER BY S_INFO_WINDCODE,TRADE_DT " % 20080101
eod = pd.read_sql(cmd, engine)

#收益率
start_month = report_now//100*100

eod['index_code'] = [ int(x[4:8]) for x in eod['IndexCode'] ]
eod['block'] = eod['index_code'].replace( industry_dic )
eod = eod[ ~eod['index_code'].isin([5029,5030]) ]

def cal_rep(x):
    month = x//100%100
    if month <= 3:
        return yymm_dic[ (x//10000-1)*100+12 ]
    elif month <= 6:
        return yymm_dic[ (x//10000)*100+3 ]
    elif month <= 9:
        return yymm_dic[ (x//10000)*100+6 ]
    else:
        return yymm_dic[ (x//10000)*100+9 ]


eod['last_rep'] = yymm_dic[report_now//100]
temp = eod[['index_code','Date','S_DQ_CLOSE']].copy()
temp.columns = ['index_code','last_rep','pre_close']
eod = eod.merge( temp,on=['index_code','last_rep'] )
eod['Return'] = eod['S_DQ_CLOSE']/eod['pre_close'] - 1

br = eod.dropna().groupby('Date').apply( lambda x:\
                       x.groupby('block')['Return'].mean() )


import datetime

start_dt = report_now
year = datetime.date.today().year
report_list = [ x + y*10000 for x in [331,630,930,1231] for y in \
               range(start_dt//10000,year+1) ]
report_list.sort()

def cal_dt_2(x,dt_list):
    if x%10000 == 331:
        start = x//10000*10000 + 400
        end = x//10000*10000 + 900
        return dt_list[(dt_list>start)&(dt_list<end)]
    elif x%10000 == 630:
        start = x//10000*10000 + 700
        end = x//10000*10000 + 1100
        return dt_list[(dt_list>start)&(dt_list<end)]
    elif x%10000 == 930:
        start = x//10000*10000 + 1000
        end = (x//10000+1)*10000 + 200
        return dt_list[(dt_list>start)&(dt_list<end)]
    elif x%10000 == 1231:
        start = (x//10000+1)*10000 + 100
        end = (x//10000+1)*10000 + 500
        return dt_list[(dt_list>start)&(dt_list<end)]
    

report_tradingDt_dic = { x:cal_dt_2(x,monthly['Date']) for x in report_list }


report_list = [ x + y*10000 for x in [331,630,930,1231] for y in \
               range(2005,2030) ]
report_list.sort()

dt_daily = monthly['Date']
ann_list = []
for rep in report_list:
    dt = dt_daily[dt_daily>rep]
    if len(dt)<15:
        break
    dt = dt.iloc[14]
    ann_list.append( dt )

tradingDate_report_ann_dic = { report_list[i]:[ ann_list[i],ann_list[i+1] ] \
                                  for i in range( len(ann_list)-1 ) }

rep_dic = {report_now: report_tradingDt_dic[report_now]}


cmd = "select cast(substr(S_INFO_WINDCODE,1,6) as signed) stock_code, \
    cast(TRADE_DT as signed) Date, S_DQ_ADJCLOSE/S_DQ_ADJPRECLOSE - 1 ret \
  from AShareEODPrices where TRADE_DT>= '20200101'"
price_table = pd.read_sql(cmd,engine)
price_table.columns = ['stock_code','Date','Return']
price_table = price_table.sort_values(['stock_code','Date'])

def cal_temp(ft):
    ft1=ft.sort_values('Date')
    ft1['NAV'] = (ft1['Return']+1).cumprod()
    return ft1

stock_nav_table_daily_dic = {}
for key in rep_dic.keys():
    rep_dateList = rep_dic[key]
    if len(rep_dateList) == 0:
        continue
    else:
        rep_dateList = rep_dateList.to_frame()
    nav_table_daily_temp = price_table.merge(rep_dateList,on=['Date'])
    nav_table_daily_temp = nav_table_daily_temp.groupby('stock_code').apply(\
                                            lambda x:cal_temp(x) )
    nav_table_daily_temp.index = range(len(nav_table_daily_temp))
    stock_nav_table_daily_dic[key] = nav_table_daily_temp
 

dt_array = monthly['Date'].values
num = 20
dt_col = []
w_col = []
roll_dt_col = []
for i in range(len(dt_array)):
    if i< num :
        continue
    dt = dt_array[i]
    sub_dt = dt_array[(i-num+1):(i+1)]
    weight = np.arange(1/num,1+1/num,1/num)
    dt_col.extend([dt]*num)
    w_col.extend(weight)
    roll_dt_col.extend(sub_dt)
tw = pd.DataFrame({'Trade_dt':dt_col,'Date':roll_dt_col,'weight':w_col})



#上市日
tw = tw[tw['Date']!=20190722 ]
tw = tw[tw['Date']!=20190808 ]


path1 = "fund_port_ori.csv"
stock_table_ori = pd.read_csv(path1)

path2 = "fund_port_par.csv"
stock_table_par = pd.read_csv(path2)

stock_table_all = pd.concat([ stock_table_ori[['fund_code','stock_code','report_period','weight']],\
                              stock_table_par[['fund_code','stock_code','report_period','weight']] ],\
                                ignore_index=True )

stock_table_all = stock_table_all[stock_table_all['report_period']==report_now]



if dt_now in trading_monthly1['Date'].values:

    date = dt_now
    
    ind_name = ['石油石化','煤炭', '有色金属', '电力及公用事业', '钢铁', '基础化工', '建筑', '建材', '轻工制造', '机械',
           '电力设备', '国防军工', '汽车', '商贸零售', '消费者服务', '家电', '纺织服装', '医药', '食品饮料',
           '农林渔牧', '银行', '非银行金融', '房地产', '交通运输', '电子元器件', '通信', '计算机', '传媒',
           '综合','综合金融']
    
    chunks1 = []
    chunks2 = []
    for name in ['original','partial']:
        #行业配置
        t1 = pd.read_csv("fund_benchmark_"+name+"190815.csv")
        t1 = t1.merge(industry_citics,on=['Code','Date'] )
        tt = t1.groupby(['Date','Industry'])['Weight'].sum()    
        tt = tt.reset_index().pivot(index='Date',columns='Industry')
        tt = tt.fillna(0).tail(1).copy()
        chunks1.append(tt)
        
        #仓位风格
        if name == 'original':
            fund_pos_size_bp_ori = pd.read_csv( "fund_pos_size_bp_ori2.csv")
        elif name == 'partial':
            fund_pos_size_bp_ori = pd.read_csv("fund_pos_size_bp_par2.csv")
        
        fund_pos_size_bp_ori['r_bb'] = fund_pos_size_bp_ori['r_bb']/fund_pos_size_bp_ori['fore_pos']
        fund_pos_size_bp_ori['r_sb'] = fund_pos_size_bp_ori['r_sb']/fund_pos_size_bp_ori['fore_pos']
        fund_pos_size_bp_ori['r_ss'] = fund_pos_size_bp_ori['r_ss']/fund_pos_size_bp_ori['fore_pos']
        fund_pos_size_bp_ori['r_bs'] = fund_pos_size_bp_ori['r_bs']/fund_pos_size_bp_ori['fore_pos']
            
        fund_pos_size_bp_ori_median = fund_pos_size_bp_ori.groupby('Trade_dt')[['r_ss','r_sb', 'r_bs','r_bb']].median()
        fund_pos_size_bp_ori_median = fund_pos_size_bp_ori_median.apply( \
                lambda x:x/np.sum(fund_pos_size_bp_ori_median,axis=1).values,axis=0)
        
        fund_pos_size_bp_ori_median.index = fund_pos_size_bp_ori_median.index//100
        
        pos_median = fund_pos_size_bp_ori_median.tail(1).copy()
        pos_median['fore_pos'] = fund_pos_size_bp_ori.groupby('Trade_dt')[
                'fore_pos'].median().values[-1]
        chunks2.append(pos_median) 
         
    ind_chunk = pd.concat(chunks1,axis=0).T
    ind_chunk.columns = ['普通股票型','偏股混合型']
    ind_chunk.index = ind_name
    
    pos_chunk = pd.concat(chunks2)
    pos_chunk.columns = ['小盘成长','小盘价值','大盘成长','大盘价值','股票仓位']
    pos_chunk['类型'] = ['普通股票型','偏股混合型']
    pos_chunk = pos_chunk[['类型','股票仓位','小盘成长','小盘价值','大盘成长','大盘价值']]
    pos_chunk.index = np.repeat(date,len(pos_chunk))
    for name in ['小盘成长','小盘价值','大盘成长','大盘价值']:
        pos_chunk[name] = pos_chunk[name]*pos_chunk['股票仓位']
        
    pos_history = pd.read_excel("pos_ts.xlsx",dtype={'日期':str})
    t = pos_chunk['股票仓位']
    t = pd.Series([str(dt_now),t.iloc[0],t.iloc[1]],index=["日期","普通股票型",'偏股混合型'])
    
    pos_history = pos_history._append(t,ignore_index=True).set_index('日期')
    pos_q = pos_history[pos_history.index>'20160101']
    pos_q = pos_q.rank().iloc[-1] / len(pos_q)
    pos_chunk['分位点'] = pos_q.values
    
    #分位点
    ind_ori = pd.read_csv('ind_ori.csv', encoding='gb2312' ).set_index('Date')
    ind_ori1 = ind_ori[ind_ori.index>=20160101]
    temp = ind_chunk['普通股票型']
    temp.name = dt_now
    ind_ori_pct = ( (ind_ori1.sub(temp,1)<0).sum() + \
                   ((ind_ori1.sub(temp,1)==0).sum()/2) ) / len(ind_ori1)
    ind_ori2 = ind_ori1[ind_ori1.index>20200101]['综合金融']
    ind_ori_pct['综合金融'] = (np.sum(ind_ori2 - temp['综合金融'] > 0 ) + \
                        np.sum(ind_ori2 - temp['综合金融'] == 0 )/2 ) / len(ind_ori2)
    ind_ori = ind_ori._append(temp)
    
    
    ind_par = pd.read_csv('ind_par.csv', encoding='gb2312' ).set_index('Date')
    ind_par1 = ind_par[ind_par.index>=20160101]
    temp = ind_chunk['偏股混合型']
    temp.name = dt_now
    ind_par_pct = ( (ind_par1.sub(temp,1)<0).sum() + \
                   ((ind_par1.sub(temp,1)==0).sum()/2) ) / len(ind_par1)
    ind_par2 = ind_par1[ind_par1.index>20200101]['综合金融']
    ind_par_pct['综合金融'] = (np.sum(ind_par2 - temp['综合金融'] > 0 ) + \
                        np.sum(ind_par2 - temp['综合金融'] == 0 )/2 ) / len(ind_par2)
    ind_par = ind_par._append(temp)
    
    ind_pct_chunk = pd.concat([ind_ori_pct,ind_par_pct],axis=1)
    ind_pct_chunk.columns = ['普通股票型','偏股混合型']
    
    # 保存CSV文件
    ind_ori.to_csv('ind_ori.csv', encoding='gb2312')
    ind_par.to_csv('ind_par.csv', encoding='gb2312')

    # 准备股票表格数据
    stock_tables = {}
    for name in ['original','partial']:
        path = "fund_benchmark_adj_"+name+"190815.csv"
        table = pd.read_csv(path)
        table = table[table['Date']==date]
        table['股票代码'] = [ str(x).zfill(6)+'.SZ' if x<600000 else
                           str(x).zfill(6)+".SH" for x in table['Code'] ]
        table['权重'] = table['Weight']
        table = table[['股票代码','权重']].set_index("股票代码")
        if name == "original":
            stock_tables['普通股票型'] = table
        else:
            stock_tables['偏股混合型'] = table

    # 使用pandas的ExcelWriter写入Excel文件
    excel_filename = 'DT' + str(date) + ".xlsx"
    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        # 写入仓位sheet
        ind_chunk.to_excel(writer, sheet_name='仓位', startrow=0, startcol=0)
        pos_chunk.to_excel(writer, sheet_name='仓位', startrow=0, startcol=4)
        ind_pct_chunk.to_excel(writer, sheet_name='仓位', startrow=0, startcol=13)
        
        # 写入股票表格
        for sheet_name, table in stock_tables.items():
            table.to_excel(writer, sheet_name=sheet_name)
    
    pos_history.to_excel( "pos_ts.xlsx")
    
    print(f"Excel文件已保存: {excel_filename}")

    # sys.exit()



def getPortfiloRet(fund_portfolio,stock_nav_table_dic,trading_monthly1,split_type='MV',NUM=300):
    def cal_fund_ret_sab(ft):    
        def f_1(ft,price_table_return):
            
            fft = ft.merge( price_table_return,on=['stock_code'] )
            if len(fft) == 0:
                return
            fft['weight_new'] = fft['weight']/np.sum(ft['weight'])
            res = fft.groupby('Date').apply(lambda x: np.sum( x['NAV']*\
                                                     x['weight_new'] ) )
            res = res.reset_index()
            res.columns = ['Date','NAV']
            res['Return'] = [ res['NAV'].iloc[0] -1 ] + res['NAV'].pct_change().tolist()[1:]
            return res[['Date','Return']]
        
        #报告期数据                    
        rep = int( ft['report_period'].values[0] )
        price_table_return = stock_nav_table_dic[ rep ]
        fund_ret_sab = ft.groupby('sign').apply(f_1,price_table_return)
        code = ft['fund_code'].values[0]
        return fund_ret_sab
    
    if split_type == 'MV':
        # mv_reader = FactorLib.MarketValue.MarketValue()
        s_item = 'MV'
        NUM = NUM
        # f_split = mv_reader.getFactorValue(freq='M').copy()
    elif split_type == 'BP':
        # bp_reader = FactorLib.BP.BP()
        s_item = 'BP'
        NUM = NUM
        # f_split = bp_reader.getFactorValue(freq='M').copy()
    elif split_type == 'MV_BP':
        # calendar = Calendar()
        dt_list = trading_monthly1['Date'].values
        cmd = "select cast(substr(S_INFO_WINDCODE,1,6) as signed) Code,cast(TRADE_DT as \
            signed) Date,S_VAL_MV MV,1/S_VAL_PB_NEW BP from AShareEODDerivativeIndicator \
            where TRADE_DT in("
        for i in dt_list:
            cmd = cmd +"'%i'," % i
        cmd = cmd[:-1]+")"
        f_split = pd.read_sql(cmd, engine)
        
    elif split_type == 'Industry':
        cycle_industry = list(range(5001,5014)) +[5024,5029]
        consume_industry = [5014,5015,5016,5017,5018,5019,5020 ]
        finance_industry = [5021,5022,5023]           
        tmt_industry = [5025,5026,5027,5028]
        ind_dic = {x:1 for x in cycle_industry}
        ind_dic.update( {x:2 for x in consume_industry} )
        ind_dic.update( {x:3 for x in finance_industry} )
        ind_dic.update( {x:4 for x in tmt_industry} )
        ind_reader = Industry.CiticsIndustry.CiticsIndustry()
        f_split = ind_reader.getStockIndustry()
        f_split['sign'] = f_split['Industry'].replace(ind_dic)

        
    def cal_sign(ft,item,NUM):
        ft1 = ft.sort_values(item)
        ft1['sign'] = [0]*(len(ft)-NUM) + [1]*NUM
        return ft1
    
    def cal_sign2(ft):
        ft1 = ft.sort_values('MV')
        ft1['mv_sign'] = [0]*(len(ft)-300) + [1]*300
        ft1 = ft1.sort_values('BP')
        N = len(ft)//2
        ft1['bp_sign'] = [0]*(len(ft)-N) + [1]*N
        ft1['sign'] = ft1['mv_sign']*10 + ft1['bp_sign']
        return ft1
    
    f_split['yymm'] = [ x//100 for x in f_split['Date'] ]
    f_split = f_split[ [ (x%100)%3 == 0 for x in f_split['yymm'] ] ]
    
    if split_type == 'MV_BP':
        f_split = f_split.groupby('yymm').apply(cal_sign2)
        f_split.index = range(len(f_split))
    elif split_type == 'Industry':
        pass
    else:
        f_split = f_split.groupby('yymm').apply(cal_sign,s_item,NUM)
        f_split.index = range(len(f_split))
    
    f_split = f_split[['Code','yymm','sign']]
    f_split.columns = ['stock_code','yymm','sign']
    
    fund_portfolio['yymm']=[x//100 for x in fund_portfolio['report_period'] ]
    fund_portfolio=fund_portfolio.merge(f_split,on=['stock_code','yymm'])
    
    fund_portfolio_ret=fund_portfolio.groupby(['fund_code','report_period']).\
                                        apply( cal_fund_ret_sab )
    fund_portfolio_ret=fund_portfolio_ret.reset_index()
    
    def f_pivot(ft):
        ft1 = ft[['Date','sign','Return']]
        ft1 = ft1.pivot(index='Date',columns='sign',values='Return')
        return ft1
    
    # 数据转换
    fund_portfolio_ret = fund_portfolio_ret.groupby( ['fund_code','report_period']).\
                                            apply( f_pivot )
    if split_type == 'MV_BP':
        fund_portfolio_ret.rename( columns={0:'r_ss',1:'r_sb',\
                                   10:'r_bs',11:'r_bb'},inplace=True )
    elif split_type == 'Industry':
        fund_portfolio_ret.rename( columns={1:'cycle',2:'consume',\
                                   3:'finance',4:'tmt'},inplace=True )
    else:
        fund_portfolio_ret.rename( columns={0:'r_s',1:'r_b'},inplace=True)
        
    fund_portfolio_ret = fund_portfolio_ret.reset_index()
    fund_portfolio_ret = fund_portfolio_ret.fillna(0)
    
    min_dt = fund_portfolio_ret['Date'].min()-10000
    #合并
    cmd = "select F_INFO_WINDCODE fund_code,cast(PRICE_DATE as signed) Date, \
            F_NAV_ADJUSTED nav from ChinaMutualFundNAV where PRICE_DATE>%i and \
            F_INFO_WINDCODE in(" % min_dt
    for i in pd.unique(fund_portfolio_ret['fund_code']):
        cmd = cmd + "'" + i + "',"
    cmd = cmd[:-1] + ")"
    fund_ret = pd.read_sql(cmd, engine)
    
    def cal_r(ft):
        ft1 = ft.sort_values("Date")
        ft1['Return'] = ft1['nav'].pct_change()
        return ft1[['fund_code','Date','Return']]
        
    fund_ret = fund_ret.groupby('fund_code').apply(cal_r)
    fund_ret.index = range(len(fund_ret))
    fund_portfolio_ret = fund_portfolio_ret.merge(fund_ret,on=['fund_code','Date'])
    return fund_portfolio_ret

    
fund_size_bp_ret = getPortfiloRet(stock_table_all, stock_nav_table_daily_dic,
                              trading_monthly1,"MV_BP")
    

import mosek
import scipy as sc
import scipy.sparse
import numpy as np

inf = 0.0

class lst:
    
    def __init__(self):
        self.env = mosek.Env()
        
    def solve(self,y,x,weight=None,min_pos=0.6,max_pos=0.8,fix_pos=None):
        
        with self.env.Task(0, 0) as task:

            numvar = x.shape[1]
            task.appendvars( numvar )
            
            if weight is None:
                weight = np.eye( x.shape[0] )
                
            if x.shape[0]!=len(weight):
                raise Exception("wrong length of weight")
            
            weight = np.diag(np.sqrt( weight ) )
            
            y = weight.dot(y)
            x = weight.dot(x)
            Q_object = (x.T.dot(x) * 2)
            Q_object = np.tril(Q_object)

            Q_object =  scipy.sparse.coo_matrix(Q_object)
            i_obj = Q_object.row.tolist()
            j_obj = Q_object.col.tolist()
            v_obj = Q_object.data.tolist()
            task.putqobj(i_obj, j_obj, v_obj)

            c_obj = -2 * y.dot(x)

            low_bon = [0]*numvar
            up_bon = [1]*numvar
            type_bon = [mosek.boundkey.ra]*numvar
            
            task.appendcons(1)
            
            for j in range(numvar):
                task.putcj(j, c_obj[j])
                task.putvarbound(j, type_bon[j], low_bon[j], up_bon[j])
                
            task.putarow( 0, range(numvar), [1]*numvar )   
            
            if fix_pos is None:
                task.putconbound(0,mosek.boundkey.ra,min_pos,max_pos)
            else:
                task.putconbound(0,mosek.boundkey.fx,fix_pos,fix_pos)
            
            task.putobjsense(mosek.objsense.minimize)
            task.optimize()
            solsta = task.getsolsta(mosek.soltype.itr)
            xx = [0.] * numvar
            task.getxx(mosek.soltype.itr,xx)
        
            # if solsta == mosek.solsta.optimal or solsta == mosek.solsta.near_optimal:
            #     pass
            # elif solsta == mosek.solsta.dual_infeas_cer:
            #     print("Primal or dual infeasibility.\n")
            # elif solsta == mosek.solsta.prim_infeas_cer:
            #     print("Primal or dual infeasibility.\n")
            # elif solsta == mosek.solsta.near_dual_infeas_cer:
            #     print("Primal or dual infeasibility.\n")
            # elif solsta == mosek.solsta.near_prim_infeas_cer:
            #     print("Primal or dual infeasibility.\n")
            # elif mosek.solsta.unknown:
            #     print("Unknown solution status")
            # else:
            #     print("Other solution status")
            return xx


def cal_pos_reg_wind(ft,cols=['r_big','r_small'],fix_pos=None,
                     fund_type='partial', tradingDate_report_ann_dic=None):
    if len(ft) < 10:
        return
    
    rep = int( ft['report_period'].iloc[0] )
    dt = int( ft['Trade_dt'].iloc[0] )
    
    tradingDate_report_ann_dic = tradingDate_report_ann_dic
    ann = tradingDate_report_ann_dic[rep][0]
    next_ann = tradingDate_report_ann_dic[rep][1]
    if dt<=ann or dt>next_ann:
        return

    y = ft['Return']
    x = ft[cols]
    w = ft['weight']
    if fund_type == 'partial':
        min_pos = 0.3
    elif fund_type == 'original':
        if dt < 20150800:
            min_pos = 0.6
        else:
            min_pos = 0.8
    elif fund_type == 'flex':
        min_pos = 0.0
        
    max_pos = 0.95
    
    leastSquare_solver = lst()
    if not fix_pos is None:
        fix_pos = ft['fore_pos'].iloc[0]
        alc = leastSquare_solver.solve(y,x,w,min_pos,max_pos,fix_pos)
    else:
        alc = leastSquare_solver.solve(y,x,w,min_pos,max_pos)

    w = np.diag(np.sqrt(w))
    y = w.dot(y)
    x = w.dot(x)
    y_fit = x.dot(alc)
    msm = np.sum( (y_fit-y.mean())**2 )
    mse = np.sum( (y-y_fit)**2 ) / (len(y)-1)
    f_value = msm/mse
    return pd.Series( alc+[f_value] ,index = cols+['F'])
    
    
def estimate(smb2_ret_table, regWeight_table, dt_list=None, items=['r_b','r_s'],
             fund_type='partial', tradingDate_report_ann_dic=None):
    if not dt_list is None:
        regWeight_table = regWeight_table[ regWeight_table['Trade_dt'].\
                                            isin(dt_list) ]
    table_for_PosReg = regWeight_table.merge( smb2_ret_table,on=['Date'] )
    # print(table_for_PosReg)
    pos_table = table_for_PosReg.groupby(['fund_code','Trade_dt','report_period']).apply( \
        lambda x: cal_pos_reg_wind(x,items,None,fund_type,tradingDate_report_ann_dic) )
    pos_table = pos_table.dropna()
    pos_table = pos_table.reset_index()
    # print(pos_table)
    pos_table['fore_pos'] = np.sum( pos_table[items],axis=1 ).values
    if len(items)==2:
        pos_table.rename(columns={'r_b':'PosLarge','r_s':'PosSmall'},inplace=True)
    return pos_table
    
    
    
daily_dt = monthly['Date']
dt_list = daily_dt[daily_dt<=dt_now].values[-5:]


    #普通股票
temp = stock_table_ori[['fund_code','report_period']].drop_duplicates()
fund_size_bp_ori = fund_size_bp_ret.merge(temp,on=['fund_code','report_period'])
fund_size_bp_ori = fund_size_bp_ori[fund_size_bp_ori['Date']<=dt_now]
t = fund_size_bp_ori.groupby('fund_code').size()
median_size = t.median()
t = t[t!=median_size].index.values
print(len(t),median_size)
fund_size_bp_ori = fund_size_bp_ori[~fund_size_bp_ori['fund_code'].isin(t)]

fund_pos_size_bp_ori = estimate(fund_size_bp_ori, tw,\
                            dt_list, ['r_ss','r_sb','r_bs','r_bb'],
                        'original', tradingDate_report_ann_dic)

cols = ['r_ss', 'r_sb', 'r_bs','r_bb', 'F', 'fore_pos']
fund_pos_size_bp_ori = fund_pos_size_bp_ori.groupby('fund_code')[cols].mean()
fund_pos_size_bp_ori = fund_pos_size_bp_ori.reset_index()
fund_pos_size_bp_ori['Trade_dt'] = dt_now


    #偏股混合
temp = stock_table_par[['fund_code','report_period']].drop_duplicates()
fund_size_bp_par = fund_size_bp_ret.merge(temp,on=['fund_code','report_period'])
fund_size_bp_par = fund_size_bp_par[fund_size_bp_par['Date']<=dt_now]
t = fund_size_bp_par.groupby('fund_code').size()
median_size = t.median()
t = t[t!=median_size].index.values
print('bad',len(t),median_size)
fund_size_bp_par = fund_size_bp_par[~fund_size_bp_par['fund_code'].isin(t)]

fund_pos_size_bp_par = estimate(fund_size_bp_par, tw,\
                            dt_list, ['r_ss','r_sb','r_bs','r_bb'],
                            'partial', tradingDate_report_ann_dic)

cols = ['r_ss', 'r_sb', 'r_bs','r_bb', 'F', 'fore_pos']
fund_pos_size_bp_par = fund_pos_size_bp_par.groupby('fund_code')[cols].mean()
fund_pos_size_bp_par = fund_pos_size_bp_par.reset_index()
fund_pos_size_bp_par['Trade_dt'] = dt_now



import mosek
import numpy as np
from cvxopt import matrix
from cvxopt import solvers
solvers.options['mosek'] = {mosek.iparam.log: 0}
solvers.options['show_progress'] = False


def adj_style_new(ft1,fund_pos_size_bp_ori_median,br,dt):
    ft = ft1.copy()
    cur_w = ft1['weight'].values
    num_var = len(ft)
    X_array = np.diag(np.ones(num_var))
    P = matrix( X_array )
    q = matrix( -1*cur_w.reshape(-1,1) )
    
    if dt == 20100930:
        bias = 0.025
    elif dt == 20150529:
        bias = 0.035
    else:
        bias = 0.02
        
    G = np.diag(np.ones(num_var))
    G = np.r_[G, np.diag(-1*np.ones(num_var))]
    lower = np.where(bias-cur_w>0, 0, bias-cur_w)
    h = np.concatenate([cur_w+bias,lower])
    G = matrix( G )
    h = matrix( h.reshape(-1,1) )
    A = [ [1]*num_var ]
    b = [ 1 ]
    style_dic = {'r_ss':0,'r_sb':1,'r_bs':10,'r_bb':11 }
    style_pos = fund_pos_size_bp_ori_median.loc[dt]
    ft['new_sign'] = ft['sign']*10 + ft['bp_sign']    
    for key,sign in style_dic.items():
        value_1 = style_pos.loc[key]
        row = np.where(ft['new_sign']==sign,1,0)
        A.append(row)
        b.append(value_1)
    ind_pos = br.loc[dt]

    if dt>=20210630:
        max_adj = 0.1
        ind_pos = ind_pos - ind_pos.mean()
        ind_pos[ind_pos>max_adj]=max_adj
        ind_pos[ind_pos<-max_adj]= -max_adj
        ind_pos = ind_pos - ind_pos.mean()
    
        ind_pos_1 = ft.groupby('block')['weight'].sum()
        ind_pos = ind_pos + ind_pos_1
        ind_pos[ind_pos<0.02] = 0.02
        ind_pos = ind_pos/np.sum(ind_pos)
    else:
        max_adj = 0.2
        ind_pos = ind_pos - ind_pos.mean()
        ind_pos[ind_pos>max_adj]=max_adj
        ind_pos[ind_pos<-max_adj]= -max_adj
        ind_pos = ind_pos - ind_pos.mean()
        ind_pos_1 = ft.groupby('block')['weight'].sum()
        ind_pos = ind_pos + ind_pos_1
        ind_pos[ind_pos<0] = 0
        ind_pos=ind_pos/np.sum(ind_pos)
        
    for key,value in ind_pos.items():        
        row = np.where(ft['block']==key,1,0)
        A.append(row)
        b.append(value)    
    A = np.array(A)
    A = matrix( A,tc='d' )
    b = matrix( np.array(b).reshape(-1,1) )
    sol = solvers.qp(P=P,q=q,G=G,h=h,A=A,b=b,solver='mosek')
    if sol['status'] != 'optimal':
        raise Exception(" Not Fund optimal Solution ! ")

    res = list(sol['x'])
    ft['weight'] = res
    return ft

     
code_col=[]
date_col=[]
ind_col=[]
for a,code,industy,InDate,OutDate in Industry.itertuples():
    dateList = np.array([dt_now])
    dt = dateList[(dateList>=InDate)&(dateList<=OutDate) ]
    code_col.extend( [code]*len(dt) )
    date_col.extend( dt.tolist() )
    ind_col.extend( [industy]*len(dt) )
    
ind_citics=pd.DataFrame({'Code':code_col,'Date':date_col,'Industry':ind_col})
        

ind_name = ['石油石化','煤炭', '有色金属', '电力及公用事业', '钢铁', '基础化工', '建筑', '建材', '轻工制造', '机械',
       '电力设备', '国防军工', '汽车', '商贸零售', '消费者服务', '家电', '纺织服装', '医药', '食品饮料',
       '农林渔牧', '银行', '非银行金融', '房地产', '交通运输', '电子元器件', '通信', '计算机', '传媒',
       '综合','综合金融']

style_name = ['小盘成长','小盘价值','大盘成长','大盘价值','股票仓位','类型']

# 使用字典收集所有sheet的数据
excel_data = {}

pos_chunk = []
ind_chunk = []
for name in ['original','partial']:

    file_path = "fund_weight_table_1_"+name+".csv"
    fund_weight_table_1 = pd.read_csv(file_path)
    if name == "original":
        fund_pos_size_bp = fund_pos_size_bp_ori.copy()
    else:
        fund_pos_size_bp = fund_pos_size_bp_par.copy()
        
    fund_pos_size_bp['r_bb'] = fund_pos_size_bp['r_bb']/fund_pos_size_bp['fore_pos']
    fund_pos_size_bp['r_sb'] = fund_pos_size_bp['r_sb']/fund_pos_size_bp['fore_pos']
    fund_pos_size_bp['r_ss'] = fund_pos_size_bp['r_ss']/fund_pos_size_bp['fore_pos']
    fund_pos_size_bp['r_bs'] = fund_pos_size_bp['r_bs']/fund_pos_size_bp['fore_pos']
    

    fund_pos_size_bp_ori_median = fund_pos_size_bp.groupby('Trade_dt')[['r_ss','r_sb', 'r_bs','r_bb']].median()
    
    fund_pos_size_bp_ori_median = fund_pos_size_bp_ori_median.apply( \
            lambda x:x/np.sum(fund_pos_size_bp_ori_median,axis=1).values,axis=0)
    
    
    pos = fund_pos_size_bp.groupby('Trade_dt')['fore_pos'].median()
    
    # 最近月份   
    temp = fund_weight_table_1[fund_weight_table_1['Date']==last_month].copy()
    
    fund_weight_table_2 =  adj_style_new(temp, fund_pos_size_bp_ori_median,
                                         br, dt_now)
    
    fund_weight_table_2.index = range(len(fund_weight_table_2))
    
    fund_weight_table_2 = fund_weight_table_2[fund_weight_table_2['weight']>0]
    
    fund_weight_table_2 = fund_weight_table_2[['stock_code','Date','weight']]
    fund_weight_table_2.columns = ['Code','Date','Weight']
    fund_weight_table_2['Date'] = dt_now
    fund_weight_table_2 = fund_weight_table_2.sort_values("Weight",ascending=False)
    
    ind_res = fund_weight_table_2.merge(ind_citics[['Code','Industry']],on=['Code'])
    ind_res = ind_res.groupby("Industry")['Weight'].sum()
    
    fund_pos_size_bp_ori_median1 = fund_pos_size_bp_ori_median*pos.loc[dt_now]
    fund_pos_size_bp_ori_median1['pos'] = pos.loc[dt_now]
    
    if name == "original":
        sheet_name = "普通股票型"
        fund_pos_size_bp_ori_median1['类型'] = '普通股票型'
    else:
        sheet_name = "偏股混合型"
        fund_pos_size_bp_ori_median1['类型'] = '偏股混合型'
    fund_pos_size_bp_ori_median1.columns = style_name
    
    # ind_res.loc[5029] = 0
    # ind_res = ind_res.sort_index()
    ind_res.index  = ind_name
    fund_weight_table_2['股票代码'] = [str(x).zfill(6)+'.SZ' if x<600000 else
                       str(x).zfill(6)+".SH" for x in fund_weight_table_2['Code'] ]
    fund_weight_table_2['权重'] = fund_weight_table_2['Weight']
    fund_weight_table_2 = fund_weight_table_2[['股票代码','权重']].sort_values(
            "权重",ascending=False).set_index("股票代码")
    
    # 保存到字典中
    excel_data[sheet_name] = fund_weight_table_2
    
    ind_chunk.append(ind_res)
    pos_chunk.append(fund_pos_size_bp_ori_median1.reset_index())



ind_chunk = pd.concat(ind_chunk,axis=1)
ind_chunk.columns = ['普通股票型','偏股混合型']

pos_chunk = pd.concat(pos_chunk)
pos_chunk = pos_chunk[['Trade_dt','类型','股票仓位','小盘成长','小盘价值','大盘成长','大盘价值']]
pos_chunk = pos_chunk.set_index("Trade_dt")

pos_history = pd.read_excel("pos_ts.xlsx",dtype={'日期':str})
t = pos_chunk['股票仓位']
t = pd.Series([str(dt_now),t.iloc[0],t.iloc[1]],index=["日期","普通股票型",'偏股混合型'])

pos_history = pos_history._append(t,ignore_index=True).set_index('日期')
pos_q = pos_history[pos_history.index>'20160101']
pos_q = pos_q.rank().iloc[-1] / len(pos_q)
pos_chunk['分位点'] = pos_q.values

# 保存仓位数据
excel_data['仓位'] = None  # 这个sheet需要特殊处理


#分位点
ind_ori = pd.read_csv('ind_ori.csv', encoding='gb2312' ).set_index('Date')
ind_ori1 = ind_ori[ind_ori.index>=20160101]
temp = ind_chunk['普通股票型']
temp.name = dt_now
ind_ori_pct = ( (ind_ori1.sub(temp,1)<0).sum() + \
               ((ind_ori1.sub(temp,1)==0).sum()/2) ) / len(ind_ori1)
ind_ori2 = ind_ori1[ind_ori1.index>20200101]['综合金融']
ind_ori_pct['综合金融'] = (np.sum(ind_ori2 - temp['综合金融'] > 0 ) + \
                    np.sum(ind_ori2 - temp['综合金融'] == 0 )/2 ) / len(ind_ori2)
ind_ori = ind_ori._append(temp)


ind_par = pd.read_csv('ind_par.csv', encoding='gb2312' ).set_index('Date')
ind_par1 = ind_par[ind_par.index>=20160101]
temp = ind_chunk['偏股混合型']
temp.name = dt_now
ind_par_pct = ( (ind_par1.sub(temp,1)<0).sum() + \
               ((ind_par1.sub(temp,1)==0).sum()/2) ) / len(ind_par1)
ind_par2 = ind_par1[ind_par1.index>20200101]['综合金融']
ind_par_pct['综合金融'] = (np.sum(ind_par2 - temp['综合金融'] > 0 ) + \
                    np.sum(ind_par2 - temp['综合金融'] == 0 )/2 ) / len(ind_par2)
ind_par = ind_par._append(temp)

ind_pct_chunk = pd.concat([ind_ori_pct,ind_par_pct],axis=1)
ind_pct_chunk.columns = ['普通股票型','偏股混合型']

# 保存CSV文件
ind_ori.to_csv('ind_ori.csv', encoding='gb2312')
ind_par.to_csv('ind_par.csv', encoding='gb2312')

pos_history.to_excel( "pos_ts.xlsx")

# 使用pandas的ExcelWriter写入所有数据
excel_filename = 'DT' + str(dt_now) + ".xlsx"
with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
    # 写入仓位sheet（特殊处理）
    ind_chunk.to_excel(writer, sheet_name='仓位', startrow=0, startcol=0)
    pos_chunk.to_excel(writer, sheet_name='仓位', startrow=0, startcol=4)
    ind_pct_chunk.to_excel(writer, sheet_name='仓位', startrow=0, startcol=13)
    
    # 写入其他sheets
    for sheet_name, data in excel_data.items():
        if sheet_name != '仓位' and data is not None:
            data.to_excel(writer, sheet_name=sheet_name)

print(f"Excel文件已保存: {excel_filename}")


#%%


# import Industry.CiticsIndustry
# import DataBase.Constant

# ind_reader = Industry.CiticsIndustry.CiticsIndustry()

# ind = ind_reader.getStockIndustry(freq='M')
# ind_name = DataBase.Constant.industry_citics

# fund_ori = pd.read_csv('C:\\Users\\<USER>\\Desktop\\周报\\基金风格\\fund_benchmark_original190815.csv')
# fund_ori = fund_ori.merge(ind, on=['Code','Date'])
# ind_ori = fund_ori.groupby(['Date','Industry'])['Weight'].sum().reset_index()
# ind_ori = ind_ori.pivot(index='Date',columns='Industry',values='Weight')
# ind_ori = ind_ori.fillna(0)
# ind_ori.columns = ind_name
# ind_ori.to_csv('C:\\Users\\<USER>\\Desktop\\周报\\基金风格\\ind_ori.csv')

# fund_ori = pd.read_csv('C:\\Users\\<USER>\\Desktop\\周报\\基金风格\\fund_benchmark_partial190815.csv')
# fund_ori = fund_ori.merge(ind, on=['Code','Date'])
# ind_ori = fund_ori.groupby(['Date','Industry'])['Weight'].sum().reset_index()
# ind_ori = ind_ori.pivot(index='Date',columns='Industry',values='Weight')
# ind_ori = ind_ori.fillna(0)
# ind_ori.columns = ind_name
# ind_ori.to_csv('C:\\Users\\<USER>\\Desktop\\周报\\基金风格\\ind_par.csv')


